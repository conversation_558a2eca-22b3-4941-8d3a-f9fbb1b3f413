package com.custommodgui.gui.animation;

public class AnimationHelper {
    /**
     * Easing funkce pro smooth animace
     */
    public static float easeInOut(float t) {
        return t * t * (3.0f - 2.0f * t);
    }
    
    public static float easeOut(float t) {
        return 1.0f - (1.0f - t) * (1.0f - t);
    }
    
    public static float easeIn(float t) {
        return t * t;
    }
    
    /**
     * Lineární interpolace mezi dvěma hodnotami
     */
    public static float lerp(float start, float end, float t) {
        return start + (end - start) * t;
    }
    
    /**
     * Interpolace barev
     */
    public static int lerpColor(int startColor, int endColor, float t) {
        int startA = (startColor >> 24) & 0xFF;
        int startR = (startColor >> 16) & 0xFF;
        int startG = (startColor >> 8) & 0xFF;
        int startB = startColor & 0xFF;
        
        int endA = (endColor >> 24) & 0xFF;
        int endR = (endColor >> 16) & 0xFF;
        int endG = (endColor >> 8) & 0xFF;
        int endB = endColor & 0xFF;
        
        int a = (int) lerp(startA, endA, t);
        int r = (int) lerp(startR, endR, t);
        int g = (int) lerp(startG, endG, t);
        int b = (int) lerp(startB, endB, t);
        
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
    
    /**
     * Omezí hodnotu mezi min a max
     */
    public static float clamp(float value, float min, float max) {
        return Math.max(min, Math.min(max, value));
    }
    
    /**
     * Omezí hodnotu mezi 0 a 1
     */
    public static float clamp01(float value) {
        return clamp(value, 0.0f, 1.0f);
    }
}
