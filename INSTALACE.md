# Instalace CustomModGUI

## 📋 Požadavky

- **Minecraft**: 1.21.4
- **Java**: 21 nebo novější
- **<PERSON><PERSON><PERSON>**: 0.16.9 nebo novější
- **Fabric API**: 0.110.5+1.21.4

## 🚀 Rychlá instalace

### 1. Příprava prostředí

1. Nainstalujte **Fabric Loader** pro Minecraft 1.21.4
   - Stáhněte z: https://fabricmc.net/use/installer/
   - Spusťte installer a vyberte verzi 1.21.4

2. Stáhněte **Fabric API**
   - Stáhněte z: https://modrinth.com/mod/fabric-api
   - Verze: 0.110.5+1.21.4

### 2. Kompilace modu

#### Windows:
```cmd
build.bat
```

#### Linux/Mac:
```bash
chmod +x build.sh
./build.sh
```

#### Manuálně:
```cmd
gradlew clean
gradlew build
```

### 3. Instalace

1. Zkopírujte `build/libs/custommodgui-1.0.0.jar` do složky `mods` ve vašem Minecraft profilu
2. Zkopírujte `Fabric API` JAR do stejné složky `mods`
3. Spusťte Minecraft s Fabric profilem

## 📁 Konfigurace

Po prvním spuštění se automaticky vytvoří:

```
.minecraft/config/custommodgui/
├── commands.yml          # Hlavní příkazy (zkopíruje se z projektu)
├── custom_commands.yml    # Vaše vlastní příkazy
└── settings.yml          # Nastavení modu
```

### Přizpůsobení příkazů

1. Upravte `config/custommodgui/commands.yml` podle vašich potřeb
2. Nebo zkopírujte váš existující `commands.yml` do config složky
3. Restartujte Minecraft nebo znovu načtěte mod

## 🎮 První použití

1. Spusťte Minecraft
2. Připojte se na server nebo spusťte singleplayer
3. Stiskněte **F7** pro otevření GUI
4. Vyberte kategorii a klikněte na příkaz

## ⚙️ Nastavení

### Tmavý/světlý režim
- Klikněte na tlačítko v pravém horním rohu GUI

### Vlastní příkazy
- Klikněte na **"+ Přidat příkaz"** v GUI
- Vyplňte formulář a uložte

### Pokročilé nastavení
Upravte `config/custommodgui/settings.yml`:

```yaml
darkMode: true              # Tmavý režim
showAnimations: true        # Animace
chatHistorySize: 100        # Velikost chat historie
themeType: "default"        # Typ tématu (default, blue)
```

## 🔧 Řešení problémů

### Mod se nenačte
- Zkontrolujte verzi Fabric Loader (0.16.9+)
- Zkontrolujte verzi Fabric API (0.110.5+1.21.4)
- Zkontrolujte verzi Javy (21+)

### GUI se neotevře
- Zkontrolujte, zda je klávesa F7 správně namapována
- Zkontrolujte logy pro chybové zprávy

### Příkazy nefungují
- Ujistěte se, že máte oprávnění na serveru
- Zkontrolujte formát příkazů v YAML souboru

### YAML chyby
- Zkontrolujte syntax YAML souboru
- Ujistěte se, že jsou správně odsazené mezery (ne taby)

## 📞 Podpora

Pokud máte problémy:

1. Zkontrolujte logy Minecraft
2. Ověřte verze všech komponent
3. Zkuste znovu vytvořit config soubory
4. Vytvořte issue v repository s detaily problému

## 🎯 Testování

Pro testování bez instalace:

```cmd
test.bat
```

nebo

```bash
./gradlew runClient
```

Toto spustí testovací instanci Minecraft s modem.
