package com.custommodgui.gui.components;

import com.custommodgui.config.ConfigManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.Drawable;
import net.minecraft.client.gui.Element;
import net.minecraft.client.gui.Selectable;

import java.util.ArrayList;
import java.util.List;

public class ChatConsole implements Drawable, Element, Selectable {
    private final int x, y, width, height;
    private final boolean isDarkMode;
    private final List<String> messages;
    private final int maxMessages;
    private int scrollOffset = 0;
    
    // Barvy pro tmavý režim
    private static final int DARK_BACKGROUND = 0xFF1E1E1E;
    private static final int DARK_BORDER = 0xFF555555;
    private static final int DARK_TEXT = 0xFFFFFFFF;
    private static final int DARK_SCROLLBAR = 0xFF666666;
    
    // Barvy pro světlý režim
    private static final int LIGHT_BACKGROUND = 0xFFFAFAFA;
    private static final int LIGHT_BORDER = 0xFFCCCCCC;
    private static final int LIGHT_TEXT = 0xFF000000;
    private static final int LIGHT_SCROLLBAR = 0xFF999999;

    public ChatConsole(int x, int y, int width, int height, boolean isDarkMode) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.isDarkMode = isDarkMode;
        this.maxMessages = (Integer) ConfigManager.getSetting("chatHistorySize", 100);
        this.messages = new ArrayList<>();
        
        // Přidání úvodní zprávy
        addMessage("§7CustomModGUI Chat Console");
        addMessage("§7Zde se budou zobrazovat odeslané příkazy...");
    }

    public void addMessage(String message) {
        messages.add(message);
        
        // Omezení počtu zpráv
        while (messages.size() > maxMessages) {
            messages.remove(0);
        }
        
        // Automatické scrollování dolů
        scrollToBottom();
    }

    private void scrollToBottom() {
        int lineHeight = 10;
        int visibleLines = (height - 10) / lineHeight;
        scrollOffset = Math.max(0, messages.size() - visibleLines);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Pozadí
        context.fill(x, y, x + width, y + height, 
                isDarkMode ? DARK_BACKGROUND : LIGHT_BACKGROUND);
        
        // Okraj
        context.fill(x, y, x + width, y + 1, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        context.fill(x, y + height - 1, x + width, y + height, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        context.fill(x, y, x + 1, y + height, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        context.fill(x + width - 1, y, x + width, y + height, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        
        // Header
        context.drawTextWithShadow(
                net.minecraft.client.MinecraftClient.getInstance().textRenderer,
                "Chat Console", x + 5, y + 3, isDarkMode ? DARK_TEXT : LIGHT_TEXT
        );
        
        // Zprávy
        renderMessages(context);
        
        // Scrollbar
        renderScrollbar(context);
    }

    private void renderMessages(DrawContext context) {
        int lineHeight = 10;
        int startY = y + 15; // Pod headerem
        int maxY = y + height - 5;
        int textX = x + 5;
        
        int visibleLines = (height - 20) / lineHeight;
        int startIndex = Math.max(0, scrollOffset);
        int endIndex = Math.min(messages.size(), startIndex + visibleLines);
        
        for (int i = startIndex; i < endIndex; i++) {
            int messageY = startY + (i - startIndex) * lineHeight;
            if (messageY + lineHeight > maxY) break;
            
            String message = messages.get(i);
            
            // Zpracování barevných kódů (základní)
            int textColor = isDarkMode ? DARK_TEXT : LIGHT_TEXT;
            if (message.startsWith("§7")) {
                textColor = 0xFF888888; // Šedá
                message = message.substring(2);
            } else if (message.startsWith("§c")) {
                textColor = 0xFFFF5555; // Červená
                message = message.substring(2);
            } else if (message.startsWith("§a")) {
                textColor = 0xFF55FF55; // Zelená
                message = message.substring(2);
            }
            
            // Zkrácení dlouhých zpráv
            int maxWidth = width - 25; // Prostor pro scrollbar
            String displayMessage = message;
            while (net.minecraft.client.MinecraftClient.getInstance().textRenderer
                    .getWidth(displayMessage) > maxWidth && displayMessage.length() > 0) {
                displayMessage = displayMessage.substring(0, displayMessage.length() - 1);
            }
            if (!displayMessage.equals(message)) {
                displayMessage += "...";
            }
            
            context.drawTextWithShadow(
                    net.minecraft.client.MinecraftClient.getInstance().textRenderer,
                    displayMessage, textX, messageY, textColor
            );
        }
    }

    private void renderScrollbar(DrawContext context) {
        if (messages.size() <= getVisibleLines()) return;
        
        int scrollbarX = x + width - 8;
        int scrollbarY = y + 15;
        int scrollbarHeight = height - 20;
        
        // Pozadí scrollbaru
        context.fill(scrollbarX, scrollbarY, scrollbarX + 6, scrollbarY + scrollbarHeight, 
                0x40000000);
        
        // Thumb scrollbaru
        int thumbHeight = Math.max(10, (getVisibleLines() * scrollbarHeight) / messages.size());
        int thumbY = scrollbarY + (scrollOffset * (scrollbarHeight - thumbHeight)) / 
                Math.max(1, messages.size() - getVisibleLines());
        
        context.fill(scrollbarX + 1, thumbY, scrollbarX + 5, thumbY + thumbHeight,
                isDarkMode ? DARK_SCROLLBAR : LIGHT_SCROLLBAR);
    }

    private int getVisibleLines() {
        return (height - 20) / 10;
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        if (mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height) {
            int maxScroll = Math.max(0, messages.size() - getVisibleLines());
            scrollOffset = Math.max(0, Math.min(maxScroll, scrollOffset - (int) verticalAmount * 3));
            return true;
        }
        return false;
    }

    @Override
    public SelectionType getType() {
        return SelectionType.NONE;
    }

    @Override
    public void setFocused(boolean focused) {
        // Nepotřebujeme focus
    }

    @Override
    public boolean isFocused() {
        return false;
    }
}
