package com.custommodgui.gui.theme;

import com.custommodgui.config.ConfigManager;

public class ThemeManager {
    public static class Theme {
        public final int background;
        public final int sidebar;
        public final int panel;
        public final int border;
        public final int text;
        public final int subtext;
        public final int buttonNormal;
        public final int buttonHover;
        public final int buttonPressed;
        public final int accent;
        public final int success;
        public final int warning;
        public final int error;
        
        public Theme(int background, int sidebar, int panel, int border, int text, int subtext,
                    int buttonNormal, int buttonHover, int buttonPressed, int accent,
                    int success, int warning, int error) {
            this.background = background;
            this.sidebar = sidebar;
            this.panel = panel;
            this.border = border;
            this.text = text;
            this.subtext = subtext;
            this.buttonNormal = buttonNormal;
            this.buttonHover = buttonHover;
            this.buttonPressed = buttonPressed;
            this.accent = accent;
            this.success = success;
            this.warning = warning;
            this.error = error;
        }
    }
    
    // Tmavé téma
    public static final Theme DARK_THEME = new Theme(
            0xFF2B2B2B, // background
            0xFF3C3C3C, // sidebar
            0xFF404040, // panel
            0xFF555555, // border
            0xFFFFFFFF, // text
            0xFFCCCCCC, // subtext
            0xFF4A4A4A, // buttonNormal
            0xFF5A5A5A, // buttonHover
            0xFF3A3A3A, // buttonPressed
            0xFF4CAF50, // accent (zelená)
            0xFF4CAF50, // success
            0xFFFF9800, // warning
            0xFFF44336  // error
    );
    
    // Světlé téma
    public static final Theme LIGHT_THEME = new Theme(
            0xFFF0F0F0, // background
            0xFFE0E0E0, // sidebar
            0xFFFFFFFF, // panel
            0xFFCCCCCC, // border
            0xFF000000, // text
            0xFF666666, // subtext
            0xFFE0E0E0, // buttonNormal
            0xFFF0F0F0, // buttonHover
            0xFFD0D0D0, // buttonPressed
            0xFF2196F3, // accent (modrá)
            0xFF4CAF50, // success
            0xFFFF9800, // warning
            0xFFF44336  // error
    );
    
    // Modrý tmavý motiv
    public static final Theme BLUE_DARK_THEME = new Theme(
            0xFF1E1E2E, // background
            0xFF2A2A3A, // sidebar
            0xFF313244, // panel
            0xFF45475A, // border
            0xFFCDD6F4, // text
            0xFFA6ADC8, // subtext
            0xFF383A59, // buttonNormal
            0xFF484A69, // buttonHover
            0xFF282A49, // buttonPressed
            0xFF89B4FA, // accent (modrá)
            0xFFA6E3A1, // success
            0xFFF9E2AF, // warning
            0xFFF38BA8  // error
    );
    
    private static Theme currentTheme;
    
    static {
        // Načtení tématu z konfigurace
        boolean isDarkMode = (Boolean) ConfigManager.getSetting("darkMode", true);
        String themeType = (String) ConfigManager.getSetting("themeType", "default");
        
        if (isDarkMode) {
            if ("blue".equals(themeType)) {
                currentTheme = BLUE_DARK_THEME;
            } else {
                currentTheme = DARK_THEME;
            }
        } else {
            currentTheme = LIGHT_THEME;
        }
    }
    
    public static Theme getCurrentTheme() {
        return currentTheme;
    }
    
    public static void setDarkMode(boolean darkMode) {
        String themeType = (String) ConfigManager.getSetting("themeType", "default");
        
        if (darkMode) {
            if ("blue".equals(themeType)) {
                currentTheme = BLUE_DARK_THEME;
            } else {
                currentTheme = DARK_THEME;
            }
        } else {
            currentTheme = LIGHT_THEME;
        }
        
        ConfigManager.setSetting("darkMode", darkMode);
    }
    
    public static void setThemeType(String themeType) {
        boolean isDarkMode = (Boolean) ConfigManager.getSetting("darkMode", true);
        
        if (isDarkMode) {
            if ("blue".equals(themeType)) {
                currentTheme = BLUE_DARK_THEME;
            } else {
                currentTheme = DARK_THEME;
            }
        } else {
            currentTheme = LIGHT_THEME;
        }
        
        ConfigManager.setSetting("themeType", themeType);
    }
    
    public static boolean isDarkMode() {
        return currentTheme == DARK_THEME || currentTheme == BLUE_DARK_THEME;
    }
    
    // Utility metody pro rychlý přístup k barvám
    public static int getBackground() { return currentTheme.background; }
    public static int getSidebar() { return currentTheme.sidebar; }
    public static int getPanel() { return currentTheme.panel; }
    public static int getBorder() { return currentTheme.border; }
    public static int getText() { return currentTheme.text; }
    public static int getSubtext() { return currentTheme.subtext; }
    public static int getButtonNormal() { return currentTheme.buttonNormal; }
    public static int getButtonHover() { return currentTheme.buttonHover; }
    public static int getButtonPressed() { return currentTheme.buttonPressed; }
    public static int getAccent() { return currentTheme.accent; }
    public static int getSuccess() { return currentTheme.success; }
    public static int getWarning() { return currentTheme.warning; }
    public static int getError() { return currentTheme.error; }
}
