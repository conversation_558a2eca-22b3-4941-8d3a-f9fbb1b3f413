package com.custommodgui.gui.components;

import com.custommodgui.model.Command;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;

import java.util.function.Consumer;

public class CommandButton extends ButtonWidget {
    private final Command command;
    private final boolean isDarkMode;
    private final Consumer<Command> onExecute;
    
    // Barvy pro tmavý režim
    private static final int DARK_NORMAL = 0xFF3E3E3E;
    private static final int DARK_HOVER = 0xFF4E4E4E;
    private static final int DARK_TEXT = 0xFFFFFFFF;
    private static final int DARK_SUBTEXT = 0xFFCCCCCC;
    private static final int DARK_BORDER = 0xFF555555;
    
    // Barvy pro světlý režim
    private static final int LIGHT_NORMAL = 0xFFF5F5F5;
    private static final int LIGHT_HOVER = 0xFFE5E5E5;
    private static final int LIGHT_TEXT = 0xFF000000;
    private static final int LIGHT_SUBTEXT = 0xFF666666;
    private static final int LIGHT_BORDER = 0xFFCCCCCC;

    public CommandButton(int x, int y, int width, int height, Command command, 
                        boolean isDarkMode, Consumer<Command> onExecute) {
        super(x, y, width, height, Text.literal(command.getLabel()), 
              btn -> onExecute.accept(command), DEFAULT_NARRATION_SUPPLIER);
        this.command = command;
        this.isDarkMode = isDarkMode;
        this.onExecute = onExecute;
    }

    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        int backgroundColor;
        int textColor;
        int subtextColor;
        int borderColor;
        
        if (isDarkMode) {
            backgroundColor = isHovered() ? DARK_HOVER : DARK_NORMAL;
            textColor = DARK_TEXT;
            subtextColor = DARK_SUBTEXT;
            borderColor = DARK_BORDER;
        } else {
            backgroundColor = isHovered() ? LIGHT_HOVER : LIGHT_NORMAL;
            textColor = LIGHT_TEXT;
            subtextColor = LIGHT_SUBTEXT;
            borderColor = LIGHT_BORDER;
        }
        
        // Pozadí s jemným stínem
        context.fill(getX() + 1, getY() + 1, getX() + getWidth() + 1, getY() + getHeight() + 1, 
                0x40000000); // Stín
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);
        
        // Okraj
        context.fill(getX(), getY(), getX() + getWidth(), getY() + 1, borderColor);
        context.fill(getX(), getY() + getHeight() - 1, getX() + getWidth(), getY() + getHeight(), borderColor);
        context.fill(getX(), getY(), getX() + 1, getY() + getHeight(), borderColor);
        context.fill(getX() + getWidth() - 1, getY(), getX() + getWidth(), getY() + getHeight(), borderColor);
        
        // Ikona pro plugin (jednoduchý barevný čtverec)
        int iconColor = getPluginColor(command.getPlugin());
        context.fill(getX() + 5, getY() + 5, getX() + 15, getY() + 15, iconColor);
        
        // Hlavní text (label)
        int textX = getX() + 20;
        int textY = getY() + 3;
        context.drawTextWithShadow(
                net.minecraft.client.MinecraftClient.getInstance().textRenderer,
                command.getLabel(), textX, textY, textColor
        );
        
        // Podtext (příkaz)
        String commandText = command.getCommand();
        if (commandText.length() > 50) {
            commandText = commandText.substring(0, 47) + "...";
        }
        context.drawTextWithShadow(
                net.minecraft.client.MinecraftClient.getInstance().textRenderer,
                commandText, textX, textY + 12, subtextColor
        );
        
        // Označení vlastního příkazu
        if (command.isCustom()) {
            context.drawTextWithShadow(
                    net.minecraft.client.MinecraftClient.getInstance().textRenderer,
                    "★", getX() + getWidth() - 15, getY() + 3, 0xFFFFD700
            );
        }
        
        // Hover efekt
        if (isHovered()) {
            context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), 
                    0x20FFFFFF); // Světlý overlay
        }
    }

    private int getPluginColor(String plugin) {
        // Generování barvy na základě názvu pluginu
        int hash = plugin.hashCode();
        int r = (hash & 0xFF0000) >> 16;
        int g = (hash & 0x00FF00) >> 8;
        int b = hash & 0x0000FF;
        
        // Zajištění dostatečné jasnosti
        r = Math.max(r, 100);
        g = Math.max(g, 100);
        b = Math.max(b, 100);
        
        return 0xFF000000 | (r << 16) | (g << 8) | b;
    }

    public Command getCommand() {
        return command;
    }
}
