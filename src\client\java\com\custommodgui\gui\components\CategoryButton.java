package com.custommodgui.gui.components;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;

public class CategoryButton extends ButtonWidget {
    private final String category;
    private boolean isSelected;
    private final boolean isDarkMode;
    
    // Barvy pro tmavý režim
    private static final int DARK_NORMAL = 0xFF4A4A4A;
    private static final int DARK_HOVER = 0xFF5A5A5A;
    private static final int DARK_SELECTED = 0xFF6A6A6A;
    private static final int DARK_TEXT = 0xFFFFFFFF;
    private static final int DARK_BORDER = 0xFF666666;
    
    // Barvy pro světlý režim
    private static final int LIGHT_NORMAL = 0xFFD0D0D0;
    private static final int LIGHT_HOVER = 0xFFE0E0E0;
    private static final int LIGHT_SELECTED = 0xFFF0F0F0;
    private static final int LIGHT_TEXT = 0xFF000000;
    private static final int LIGHT_BORDER = 0xFFAAAAAA;

    public CategoryButton(int x, int y, int width, int height, String category, 
                         boolean isSelected, boolean isDarkMode, PressAction onPress) {
        super(x, y, width, height, Text.literal(category), onPress, DEFAULT_NARRATION_SUPPLIER);
        this.category = category;
        this.isSelected = isSelected;
        this.isDarkMode = isDarkMode;
    }

    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        int backgroundColor;
        int textColor;
        int borderColor;
        
        if (isDarkMode) {
            backgroundColor = isSelected ? DARK_SELECTED : (isHovered() ? DARK_HOVER : DARK_NORMAL);
            textColor = DARK_TEXT;
            borderColor = DARK_BORDER;
        } else {
            backgroundColor = isSelected ? LIGHT_SELECTED : (isHovered() ? LIGHT_HOVER : LIGHT_NORMAL);
            textColor = LIGHT_TEXT;
            borderColor = LIGHT_BORDER;
        }
        
        // Pozadí s zaoblenými rohy (simulace)
        context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), backgroundColor);
        
        // Okraj
        if (isSelected) {
            context.fill(getX(), getY(), getX() + 2, getY() + getHeight(), 
                    isDarkMode ? 0xFF4CAF50 : 0xFF2196F3); // Zelená/modrá čára pro vybranou kategorii
        }
        
        // Text
        int textX = getX() + (isSelected ? 8 : 6);
        int textY = getY() + (getHeight() - 8) / 2;
        context.drawTextWithShadow(
                net.minecraft.client.MinecraftClient.getInstance().textRenderer,
                category, textX, textY, textColor
        );
        
        // Hover efekt
        if (isHovered() && !isSelected) {
            context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), 
                    0x20FFFFFF); // Světlý overlay
        }
    }

    public String getCategory() {
        return category;
    }

    public void setSelected(boolean selected) {
        this.isSelected = selected;
    }

    public boolean isSelected() {
        return isSelected;
    }
}
