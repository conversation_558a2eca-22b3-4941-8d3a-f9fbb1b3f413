package com.custommodgui.gui;

import com.custommodgui.config.ConfigManager;
import com.custommodgui.model.Command;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;

public class AddCommandScreen extends Screen {
    private final MainGuiScreen parent;
    private TextFieldWidget pluginField;
    private TextFieldWidget categoryField;
    private TextFieldWidget labelField;
    private TextFieldWidget commandField;
    private boolean isDarkMode;
    
    // Barvy pro tmavý režim
    private static final int DARK_BACKGROUND = 0xFF2B2B2B;
    private static final int DARK_PANEL = 0xFF3C3C3C;
    private static final int DARK_BORDER = 0xFF555555;
    private static final int DARK_TEXT = 0xFFFFFFFF;
    
    // Barvy pro světlý režim
    private static final int LIGHT_BACKGROUND = 0xFFF0F0F0;
    private static final int LIGHT_PANEL = 0xFFFFFFFF;
    private static final int LIGHT_BORDER = 0xFFCCCCCC;
    private static final int LIGHT_TEXT = 0xFF000000;

    public AddCommandScreen(MainGuiScreen parent) {
        super(Text.literal("Přidat vlastní příkaz"));
        this.parent = parent;
        this.isDarkMode = (Boolean) ConfigManager.getSetting("darkMode", true);
    }

    @Override
    protected void init() {
        super.init();
        
        int panelWidth = 400;
        int panelHeight = 300;
        int panelX = (width - panelWidth) / 2;
        int panelY = (height - panelHeight) / 2;
        
        int fieldWidth = 300;
        int fieldHeight = 20;
        int fieldX = panelX + (panelWidth - fieldWidth) / 2;
        int startY = panelY + 50;
        
        // Plugin field
        pluginField = new TextFieldWidget(textRenderer, fieldX, startY, fieldWidth, fieldHeight, Text.literal("Plugin"));
        pluginField.setPlaceholder(Text.literal("Název pluginu (např. CMI)"));
        pluginField.setMaxLength(50);
        addSelectableChild(pluginField);
        
        // Category field
        categoryField = new TextFieldWidget(textRenderer, fieldX, startY + 40, fieldWidth, fieldHeight, Text.literal("Kategorie"));
        categoryField.setPlaceholder(Text.literal("Kategorie (např. Admin)"));
        categoryField.setMaxLength(50);
        addSelectableChild(categoryField);
        
        // Label field
        labelField = new TextFieldWidget(textRenderer, fieldX, startY + 80, fieldWidth, fieldHeight, Text.literal("Název"));
        labelField.setPlaceholder(Text.literal("Zobrazovaný název příkazu"));
        labelField.setMaxLength(100);
        addSelectableChild(labelField);
        
        // Command field
        commandField = new TextFieldWidget(textRenderer, fieldX, startY + 120, fieldWidth, fieldHeight, Text.literal("Příkaz"));
        commandField.setPlaceholder(Text.literal("Příkaz (např. /heal {player})"));
        commandField.setMaxLength(200);
        addSelectableChild(commandField);
        
        // Nastavení focusu na první field
        setInitialFocus(pluginField);
        
        // Tlačítka
        int buttonWidth = 80;
        int buttonY = startY + 180;
        
        // Uložit tlačítko
        addDrawableChild(ButtonWidget.builder(Text.literal("Uložit"), button -> {
            saveCommand();
        }).dimensions(fieldX + fieldWidth - buttonWidth * 2 - 10, buttonY, buttonWidth, 20).build());
        
        // Zrušit tlačítko
        addDrawableChild(ButtonWidget.builder(Text.literal("Zrušit"), button -> {
            close();
        }).dimensions(fieldX + fieldWidth - buttonWidth, buttonY, buttonWidth, 20).build());
    }

    private void saveCommand() {
        String plugin = pluginField.getText().trim();
        String category = categoryField.getText().trim();
        String label = labelField.getText().trim();
        String command = commandField.getText().trim();
        
        // Validace
        if (plugin.isEmpty() || category.isEmpty() || label.isEmpty() || command.isEmpty()) {
            // TODO: Zobrazit chybovou zprávu
            return;
        }
        
        // Vytvoření nového příkazu
        Command newCommand = new Command(plugin, category, label, command, true);
        
        // Přidání do konfigurace
        ConfigManager.addCustomCommand(newCommand);
        
        // Obnovení parent GUI
        if (parent != null) {
            parent.refreshCommands();
        }
        
        // Zavření dialogu
        close();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Pozadí s průhledností
        context.fill(0, 0, width, height, 0x80000000);
        
        int panelWidth = 400;
        int panelHeight = 300;
        int panelX = (width - panelWidth) / 2;
        int panelY = (height - panelHeight) / 2;
        
        // Panel pozadí
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, 
                isDarkMode ? DARK_PANEL : LIGHT_PANEL);
        
        // Panel okraj
        context.fill(panelX, panelY, panelX + panelWidth, panelY + 1, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        context.fill(panelX, panelY + panelHeight - 1, panelX + panelWidth, panelY + panelHeight, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        context.fill(panelX, panelY, panelX + 1, panelY + panelHeight, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        context.fill(panelX + panelWidth - 1, panelY, panelX + panelWidth, panelY + panelHeight, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        
        // Titulek
        String title = "Přidat vlastní příkaz";
        int titleX = panelX + (panelWidth - textRenderer.getWidth(title)) / 2;
        context.drawTextWithShadow(textRenderer, title, titleX, panelY + 15, 
                isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        
        // Labely pro fieldy
        int fieldX = panelX + (panelWidth - 300) / 2;
        int startY = panelY + 50;
        
        context.drawTextWithShadow(textRenderer, "Plugin:", fieldX, startY - 12, 
                isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        context.drawTextWithShadow(textRenderer, "Kategorie:", fieldX, startY + 28, 
                isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        context.drawTextWithShadow(textRenderer, "Název:", fieldX, startY + 68, 
                isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        context.drawTextWithShadow(textRenderer, "Příkaz:", fieldX, startY + 108, 
                isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        
        // Vykreslení text fieldů
        pluginField.render(context, mouseX, mouseY, delta);
        categoryField.render(context, mouseX, mouseY, delta);
        labelField.render(context, mouseX, mouseY, delta);
        commandField.render(context, mouseX, mouseY, delta);
        
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // ESC zavře dialog
        if (keyCode == 256) { // ESC
            close();
            return true;
        }
        
        // Enter uloží příkaz (pokud je focus na posledním fieldu)
        if (keyCode == 257 && commandField.isFocused()) { // Enter
            saveCommand();
            return true;
        }
        
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public void close() {
        if (client != null) {
            client.setScreen(parent);
        }
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}
