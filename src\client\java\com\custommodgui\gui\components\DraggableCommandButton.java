package com.custommodgui.gui.components;

import com.custommodgui.model.Command;
import net.minecraft.client.gui.DrawContext;

import java.util.function.Consumer;

public class DraggableCommandButton extends CommandButton {
    private boolean isDragging = false;
    private int dragStartX, dragStartY;
    private int originalX, originalY;
    private float dragOffsetX, dragOffsetY;
    private Consumer<DraggableCommandButton> onDragStart;
    private Consumer<DraggableCommandButton> onDragEnd;
    private Runnable onDrop;

    public DraggableCommandButton(int x, int y, int width, int height, Command command, 
                                 boolean isDarkMode, Consumer<Command> onExecute) {
        super(x, y, width, height, command, isDarkMode, onExecute);
        this.originalX = x;
        this.originalY = y;
    }

    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        // Pokud se táhne, vykreslí se s průhledností a posunem
        if (isDragging) {
            // Uložení původní pozice
            int tempX = getX();
            int tempY = getY();
            
            // Nastavení nové pozice pro vykreslení
            setX((int) (dragStartX + dragOffsetX));
            setY((int) (dragStartY + dragOffsetY));
            
            // Vykreslení s průhledností
            context.getMatrices().push();
            context.getMatrices().translate(0, 0, 100); // Vykreslení nad ostatními prvky
            
            // Stín při táhnutí
            context.fill(getX() + 3, getY() + 3, getX() + getWidth() + 3, 
                    getY() + getHeight() + 3, 0x80000000);
            
            super.renderWidget(context, mouseX, mouseY, delta);
            
            // Overlay pro průhlednost
            context.fill(getX(), getY(), getX() + getWidth(), getY() + getHeight(), 0x40000000);
            
            context.getMatrices().pop();
            
            // Obnovení původní pozice
            setX(tempX);
            setY(tempY);
        } else {
            super.renderWidget(context, mouseX, mouseY, delta);
        }
        
        // Vykreslení drop zóny při táhnutí
        if (isDragging) {
            renderDropZone(context);
        }
    }

    private void renderDropZone(DrawContext context) {
        // Vykreslení obrysu původní pozice
        context.fill(originalX, originalY, originalX + getWidth(), originalY + 1, 0xFF00FF00);
        context.fill(originalX, originalY + getHeight() - 1, originalX + getWidth(), 
                originalY + getHeight(), 0xFF00FF00);
        context.fill(originalX, originalY, originalX + 1, originalY + getHeight(), 0xFF00FF00);
        context.fill(originalX + getWidth() - 1, originalY, originalX + getWidth(), 
                originalY + getHeight(), 0xFF00FF00);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0 && isMouseOver(mouseX, mouseY)) { // Levé tlačítko myši
            // Začátek táhnutí
            isDragging = true;
            dragStartX = getX();
            dragStartY = getY();
            dragOffsetX = 0;
            dragOffsetY = 0;
            
            if (onDragStart != null) {
                onDragStart.accept(this);
            }
            
            return true;
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && button == 0) {
            dragOffsetX += (float) deltaX;
            dragOffsetY += (float) deltaY;
            return true;
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (isDragging && button == 0) {
            // Konec táhnutí
            isDragging = false;
            
            // Kontrola, zda se má tlačítko přesunout
            int newX = (int) (dragStartX + dragOffsetX);
            int newY = (int) (dragStartY + dragOffsetY);
            
            // Pokud je dostatečně daleko od původní pozice, přesune se
            double distance = Math.sqrt(Math.pow(newX - originalX, 2) + Math.pow(newY - originalY, 2));
            if (distance > 50) { // Minimální vzdálenost pro přesunutí
                setX(newX);
                setY(newY);
                originalX = newX;
                originalY = newY;
                
                if (onDrop != null) {
                    onDrop.run();
                }
            } else {
                // Vrácení na původní pozici
                setX(originalX);
                setY(originalY);
            }
            
            if (onDragEnd != null) {
                onDragEnd.accept(this);
            }
            
            return true;
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }

    // Gettery a settery
    public boolean isDragging() {
        return isDragging;
    }

    public void setOnDragStart(Consumer<DraggableCommandButton> onDragStart) {
        this.onDragStart = onDragStart;
    }

    public void setOnDragEnd(Consumer<DraggableCommandButton> onDragEnd) {
        this.onDragEnd = onDragEnd;
    }

    public void setOnDrop(Runnable onDrop) {
        this.onDrop = onDrop;
    }

    public void resetPosition() {
        setX(originalX);
        setY(originalY);
        isDragging = false;
    }

    public void setOriginalPosition(int x, int y) {
        this.originalX = x;
        this.originalY = y;
        setX(x);
        setY(y);
    }
}
