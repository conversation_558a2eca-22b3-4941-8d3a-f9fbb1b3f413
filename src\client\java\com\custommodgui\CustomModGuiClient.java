package com.custommodgui;

import com.custommodgui.config.ConfigManager;
import com.custommodgui.gui.MainGuiScreen;
import com.custommodgui.keybinding.KeyBindings;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.minecraft.client.MinecraftClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CustomModGuiClient implements ClientModInitializer {
    public static final String MOD_ID = "custommodgui";
    public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

    @Override
    public void onInitializeClient() {
        LOGGER.info("Inicializace CustomModGUI...");

        // Inicializace key bindingů
        KeyBindings.initialize();

        // Inicializace konfigurace
        ConfigManager.initialize();

        // Registrace tick eventu pro zpracování key bindingů
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            while (KeyBindings.OPEN_GUI.wasPressed()) {
                if (client.currentScreen == null) {
                    client.setScreen(new MainGuiScreen());
                }
            }
        });

        LOGGER.info("CustomModGUI úspěšně inicializován!");
    }
}
