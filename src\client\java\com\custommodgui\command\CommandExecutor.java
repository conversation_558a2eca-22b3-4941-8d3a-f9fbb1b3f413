package com.custommodgui.command;

import com.custommodgui.CustomModGuiClient;
import com.custommodgui.model.Command;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayerEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommandExecutor {
    private static final Pattern PARAMETER_PATTERN = Pattern.compile("\\{([^}]+)\\}");
    private static final Map<String, String> parameterCache = new HashMap<>();
    
    /**
     * Bezpečně vykoná příkaz s možností nahrazení parametrů
     */
    public static boolean executeCommand(Command command) {
        MinecraftClient client = MinecraftClient.getInstance();
        ClientPlayerEntity player = client.player;
        
        if (player == null) {
            CustomModGuiClient.LOGGER.warn("Nelze vykonat příkaz - hrá<PERSON> není připojen");
            return false;
        }
        
        String commandText = command.getCommand();
        
        // Zpracování parametrů v příkazu
        commandText = processParameters(commandText);
        
        if (commandText == null) {
            CustomModGuiClient.LOGGER.warn("Příkaz zrušen uživatelem");
            return false;
        }
        
        try {
            // Logování pro debug
            CustomModGuiClient.LOGGER.info("Vykonávám příkaz: {}", commandText);
            
            // Odeslání příkazu
            if (commandText.startsWith("/")) {
                // Příkaz
                player.networkHandler.sendChatCommand(commandText.substring(1));
            } else {
                // Chat zpráva
                player.networkHandler.sendChatMessage(commandText);
            }
            
            return true;
        } catch (Exception e) {
            CustomModGuiClient.LOGGER.error("Chyba při vykonávání příkazu: {}", commandText, e);
            return false;
        }
    }
    
    /**
     * Zpracuje parametry v příkazu a nahradí je hodnotami
     */
    private static String processParameters(String commandText) {
        Matcher matcher = PARAMETER_PATTERN.matcher(commandText);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String parameter = matcher.group(1);
            String value = getParameterValue(parameter);
            
            if (value == null) {
                // Uživatel zrušil zadávání parametru
                return null;
            }
            
            matcher.appendReplacement(result, Matcher.quoteReplacement(value));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * Získá hodnotu parametru od uživatele nebo z cache
     */
    private static String getParameterValue(String parameter) {
        // Zkontrolování cache
        if (parameterCache.containsKey(parameter)) {
            String cachedValue = parameterCache.get(parameter);
            // Pro některé parametry používáme cache (např. player name)
            if (shouldUseCache(parameter)) {
                return cachedValue;
            }
        }
        
        // Získání hodnoty od uživatele
        String value = promptForParameter(parameter);
        
        if (value != null && !value.trim().isEmpty()) {
            // Uložení do cache pro budoucí použití
            parameterCache.put(parameter, value.trim());
            return value.trim();
        }
        
        return null;
    }
    
    /**
     * Určuje, zda se má pro parametr použít cache
     */
    private static boolean shouldUseCache(String parameter) {
        // Cache se používá pro parametry, které se často opakují
        return parameter.equals("player") || 
               parameter.equals("user") || 
               parameter.equals("name");
    }
    
    /**
     * Zobrazí dialog pro zadání parametru
     */
    private static String promptForParameter(String parameter) {
        MinecraftClient client = MinecraftClient.getInstance();
        
        // Pro testování vrátíme výchozí hodnoty
        // V produkční verzi by zde byl dialog nebo input field
        switch (parameter.toLowerCase()) {
            case "player":
            case "user":
                // Vrátíme jméno aktuálního hráče jako výchozí
                return client.player != null ? client.player.getName().getString() : "player";
            case "amount":
                return "1";
            case "time":
                return "1h";
            case "radius":
                return "10";
            case "reason":
                return "Důvod";
            case "item_id":
            case "id":
                return "minecraft:stone";
            case "type":
                return "SWORD";
            case "message":
                return "Zpráva";
            case "name":
                return "název";
            case "password":
            case "old_password":
            case "new_password":
                return "heslo";
            case "shop_name":
                return "shop";
            case "menu_name":
                return "menu";
            case "block":
            case "from_block":
            case "to_block":
                return "minecraft:stone";
            case "material":
                return "stone";
            case "brush_type":
                return "ball";
            case "trait_name":
                return "lookatplayers";
            case "text":
                return "Text";
            case "url":
                return "https://example.com/image.png";
            case "width":
                return "128";
            case "height":
                return "128";
            case "mob_name":
                return "Skeleton";
            case "item_name":
                return "ExampleItem";
            case "skin_name":
                return "skin";
            case "warp":
                return "spawn";
            default:
                return parameter; // Vrátíme název parametru jako výchozí hodnotu
        }
    }
    
    /**
     * Vyčistí cache parametrů
     */
    public static void clearParameterCache() {
        parameterCache.clear();
    }
    
    /**
     * Nastaví hodnotu parametru v cache
     */
    public static void setParameterValue(String parameter, String value) {
        if (value != null && !value.trim().isEmpty()) {
            parameterCache.put(parameter, value.trim());
        }
    }
    
    /**
     * Získá aktuální hodnotu parametru z cache
     */
    public static String getCachedParameterValue(String parameter) {
        return parameterCache.get(parameter);
    }
}
