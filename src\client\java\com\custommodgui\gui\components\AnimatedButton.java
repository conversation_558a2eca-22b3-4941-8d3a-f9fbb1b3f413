package com.custommodgui.gui.components;

import com.custommodgui.gui.animation.AnimationHelper;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;

public class AnimatedButton extends ButtonWidget {
    private float hoverAnimation = 0.0f;
    private float pressAnimation = 0.0f;
    private boolean wasHovered = false;
    private boolean wasPressed = false;
    private final boolean isDarkMode;
    
    // Barvy pro animace
    private static final int DARK_NORMAL = 0xFF4A4A4A;
    private static final int DARK_HOVER = 0xFF5A5A5A;
    private static final int DARK_PRESSED = 0xFF3A3A3A;
    
    private static final int LIGHT_NORMAL = 0xFFE0E0E0;
    private static final int LIGHT_HOVER = 0xFFF0F0F0;
    private static final int LIGHT_PRESSED = 0xFFD0D0D0;

    public AnimatedButton(int x, int y, int width, int height, Text message, 
                         PressAction onPress, boolean isDarkMode) {
        super(x, y, width, height, message, onPress, DEFAULT_NARRATION_SUPPLIER);
        this.isDarkMode = isDarkMode;
    }

    @Override
    public void renderWidget(DrawContext context, int mouseX, int mouseY, float delta) {
        // Aktualizace animací
        updateAnimations(delta);
        
        // Výpočet barev s animacemi
        int normalColor = isDarkMode ? DARK_NORMAL : LIGHT_NORMAL;
        int hoverColor = isDarkMode ? DARK_HOVER : LIGHT_HOVER;
        int pressedColor = isDarkMode ? DARK_PRESSED : LIGHT_PRESSED;
        
        // Interpolace barev
        int currentColor = normalColor;
        if (hoverAnimation > 0) {
            currentColor = AnimationHelper.lerpColor(normalColor, hoverColor, hoverAnimation);
        }
        if (pressAnimation > 0) {
            currentColor = AnimationHelper.lerpColor(currentColor, pressedColor, pressAnimation);
        }
        
        // Výpočet scale efektu
        float scale = 1.0f + (pressAnimation * -0.05f) + (hoverAnimation * 0.02f);
        
        // Vykreslení s animacemi
        renderAnimatedButton(context, currentColor, scale);
        
        // Text
        int textColor = isDarkMode ? 0xFFFFFFFF : 0xFF000000;
        int textX = getX() + (getWidth() - textRenderer.getWidth(getMessage())) / 2;
        int textY = getY() + (getHeight() - 8) / 2;
        
        context.drawTextWithShadow(textRenderer, getMessage(), textX, textY, textColor);
    }

    private void updateAnimations(float delta) {
        float animationSpeed = 8.0f; // Rychlost animace
        
        // Hover animace
        boolean isHovered = isHovered();
        if (isHovered && !wasHovered) {
            // Začátek hover
        } else if (!isHovered && wasHovered) {
            // Konec hover
        }
        
        float targetHover = isHovered ? 1.0f : 0.0f;
        hoverAnimation = AnimationHelper.lerp(hoverAnimation, targetHover, 
                AnimationHelper.clamp01(delta * animationSpeed));
        
        // Press animace
        boolean isPressed = isPressed();
        if (isPressed && !wasPressed) {
            // Začátek press
        } else if (!isPressed && wasPressed) {
            // Konec press
        }
        
        float targetPress = isPressed ? 1.0f : 0.0f;
        pressAnimation = AnimationHelper.lerp(pressAnimation, targetPress, 
                AnimationHelper.clamp01(delta * animationSpeed * 2)); // Rychlejší press animace
        
        wasHovered = isHovered;
        wasPressed = isPressed;
    }

    private void renderAnimatedButton(DrawContext context, int color, float scale) {
        // Výpočet pozice a velikosti s scale
        int scaledWidth = (int) (getWidth() * scale);
        int scaledHeight = (int) (getHeight() * scale);
        int scaledX = getX() + (getWidth() - scaledWidth) / 2;
        int scaledY = getY() + (getHeight() - scaledHeight) / 2;
        
        // Stín (pouze pokud je scale > 1)
        if (scale > 1.0f) {
            context.fill(scaledX + 2, scaledY + 2, scaledX + scaledWidth + 2, 
                    scaledY + scaledHeight + 2, 0x40000000);
        }
        
        // Hlavní tlačítko
        context.fill(scaledX, scaledY, scaledX + scaledWidth, scaledY + scaledHeight, color);
        
        // Okraj s gradientem
        int borderColor = isDarkMode ? 0xFF666666 : 0xFFAAAAAA;
        context.fill(scaledX, scaledY, scaledX + scaledWidth, scaledY + 1, borderColor);
        context.fill(scaledX, scaledY + scaledHeight - 1, scaledX + scaledWidth, 
                scaledY + scaledHeight, borderColor);
        context.fill(scaledX, scaledY, scaledX + 1, scaledY + scaledHeight, borderColor);
        context.fill(scaledX + scaledWidth - 1, scaledY, scaledX + scaledWidth, 
                scaledY + scaledHeight, borderColor);
        
        // Highlight efekt na horní části
        if (hoverAnimation > 0) {
            int highlightColor = isDarkMode ? 0x40FFFFFF : 0x40000000;
            int highlightAlpha = (int) (0x40 * hoverAnimation);
            highlightColor = (highlightColor & 0x00FFFFFF) | (highlightAlpha << 24);
            context.fill(scaledX + 1, scaledY + 1, scaledX + scaledWidth - 1, 
                    scaledY + scaledHeight / 2, highlightColor);
        }
    }

    private net.minecraft.client.font.TextRenderer textRenderer = 
            net.minecraft.client.MinecraftClient.getInstance().textRenderer;
}
