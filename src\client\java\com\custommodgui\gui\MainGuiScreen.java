package com.custommodgui.gui;

import com.custommodgui.command.CommandExecutor;
import com.custommodgui.config.ConfigManager;
import com.custommodgui.gui.components.CategoryButton;
import com.custommodgui.gui.components.CommandButton;
import com.custommodgui.gui.components.ChatConsole;
import com.custommodgui.model.Command;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.util.*;
import java.util.stream.Collectors;

public class MainGuiScreen extends Screen {
    private static final int SIDEBAR_WIDTH = 200;
    private static final int BUTTON_HEIGHT = 25;
    private static final int BUTTON_SPACING = 5;
    private static final int MARGIN = 10;
    
    private Map<String, List<Command>> commandsByCategory;
    private String selectedCategory = null;
    private List<CategoryButton> categoryButtons = new ArrayList<>();
    private List<CommandButton> commandButtons = new ArrayList<>();
    private ChatConsole chatConsole;
    private boolean isDarkMode;
    
    // Barvy pro tmavý režim
    private static final int DARK_BACKGROUND = 0xFF2B2B2B;
    private static final int DARK_SIDEBAR = 0xFF3C3C3C;
    private static final int DARK_BORDER = 0xFF555555;
    private static final int DARK_TEXT = 0xFFFFFFFF;
    
    // Barvy pro světlý režim
    private static final int LIGHT_BACKGROUND = 0xFFF0F0F0;
    private static final int LIGHT_SIDEBAR = 0xFFE0E0E0;
    private static final int LIGHT_BORDER = 0xFFCCCCCC;
    private static final int LIGHT_TEXT = 0xFF000000;

    public MainGuiScreen() {
        super(Text.literal("CustomModGUI"));
        this.isDarkMode = (Boolean) ConfigManager.getSetting("darkMode", true);
        loadCommands();
    }

    private void loadCommands() {
        List<Command> allCommands = ConfigManager.getAllCommands();
        commandsByCategory = allCommands.stream()
                .collect(Collectors.groupingBy(Command::getCategory));
        
        if (!commandsByCategory.isEmpty() && selectedCategory == null) {
            selectedCategory = commandsByCategory.keySet().iterator().next();
        }
    }

    @Override
    protected void init() {
        super.init();
        
        // Vyčištění předchozích widgetů
        categoryButtons.clear();
        commandButtons.clear();
        
        // Inicializace chat konzole
        int consoleHeight = 100;
        chatConsole = new ChatConsole(
                SIDEBAR_WIDTH + MARGIN, 
                height - consoleHeight - MARGIN,
                width - SIDEBAR_WIDTH - 2 * MARGIN,
                consoleHeight,
                isDarkMode
        );
        
        // Vytvoření category tlačítek
        createCategoryButtons();
        
        // Vytvoření command tlačítek pro vybranou kategorii
        createCommandButtons();
        
        // Tlačítko pro přidání vlastního příkazu
        addDrawableChild(ButtonWidget.builder(Text.literal("+ Přidat příkaz"), button -> {
            client.setScreen(new AddCommandScreen(this));
        }).dimensions(MARGIN, height - 35, 180, 20).build());
        
        // Tlačítko pro přepnutí režimu
        addDrawableChild(ButtonWidget.builder(
                Text.literal(isDarkMode ? "Světlý režim" : "Tmavý režim"), 
                button -> toggleTheme()
        ).dimensions(width - 120, MARGIN, 100, 20).build());
        
        // Tlačítko zavřít
        addDrawableChild(ButtonWidget.builder(Text.literal("Zavřít"), button -> {
            close();
        }).dimensions(width - 120, height - 35, 100, 20).build());
    }

    private void createCategoryButtons() {
        int y = MARGIN + 30; // Prostor pro header
        
        for (String category : commandsByCategory.keySet()) {
            boolean isSelected = category.equals(selectedCategory);
            CategoryButton button = new CategoryButton(
                    MARGIN, y, SIDEBAR_WIDTH - 2 * MARGIN, BUTTON_HEIGHT,
                    category, isSelected, isDarkMode,
                    btn -> selectCategory(category)
            );
            categoryButtons.add(button);
            addDrawableChild(button);
            y += BUTTON_HEIGHT + BUTTON_SPACING;
        }
    }

    private void createCommandButtons() {
        // Vyčištění předchozích command tlačítek
        commandButtons.forEach(this::remove);
        commandButtons.clear();
        
        if (selectedCategory == null) return;
        
        List<Command> commands = commandsByCategory.get(selectedCategory);
        if (commands == null) return;
        
        int x = SIDEBAR_WIDTH + MARGIN;
        int y = MARGIN + 30;
        int buttonWidth = width - SIDEBAR_WIDTH - 2 * MARGIN;
        int maxY = height - 120 - MARGIN; // Prostor pro chat konzoli
        
        for (Command command : commands) {
            if (y + BUTTON_HEIGHT > maxY) break; // Zabránění překrytí s chat konzolí
            
            CommandButton button = new CommandButton(
                    x, y, buttonWidth, BUTTON_HEIGHT,
                    command, isDarkMode,
                    cmd -> executeCommand(cmd)
            );
            commandButtons.add(button);
            addDrawableChild(button);
            y += BUTTON_HEIGHT + BUTTON_SPACING;
        }
    }

    private void selectCategory(String category) {
        selectedCategory = category;
        
        // Aktualizace category tlačítek
        for (CategoryButton button : categoryButtons) {
            button.setSelected(button.getCategory().equals(category));
        }
        
        // Znovu vytvoření command tlačítek
        createCommandButtons();
    }

    private void executeCommand(Command command) {
        // Zobrazení v chat konzoli
        chatConsole.addMessage("§7> " + command.getCommand());

        // Vykonání příkazu pomocí CommandExecutor
        boolean success = CommandExecutor.executeCommand(command);

        if (success) {
            chatConsole.addMessage("§aPříkaz úspěšně vykonán");
        } else {
            chatConsole.addMessage("§cChyba při vykonávání příkazu");
        }
    }

    private void toggleTheme() {
        isDarkMode = !isDarkMode;
        ConfigManager.setSetting("darkMode", isDarkMode);
        
        // Znovu inicializace GUI s novým tématem
        init();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Pozadí
        context.fill(0, 0, width, height, isDarkMode ? DARK_BACKGROUND : LIGHT_BACKGROUND);
        
        // Sidebar
        context.fill(0, 0, SIDEBAR_WIDTH, height, isDarkMode ? DARK_SIDEBAR : LIGHT_SIDEBAR);
        context.fill(SIDEBAR_WIDTH, 0, SIDEBAR_WIDTH + 1, height, isDarkMode ? DARK_BORDER : LIGHT_BORDER);
        
        // Header
        context.drawTextWithShadow(textRenderer, "Kategorie", MARGIN, MARGIN, 
                isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        
        if (selectedCategory != null) {
            context.drawTextWithShadow(textRenderer, "Příkazy - " + selectedCategory, 
                    SIDEBAR_WIDTH + MARGIN, MARGIN, isDarkMode ? DARK_TEXT : LIGHT_TEXT);
        }
        
        // Vykreslení chat konzole
        chatConsole.render(context, mouseX, mouseY, delta);
        
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean shouldPause() {
        return false; // GUI nepozastaví hru
    }

    public void refreshCommands() {
        loadCommands();
        init(); // Znovu inicializace GUI
    }
}
