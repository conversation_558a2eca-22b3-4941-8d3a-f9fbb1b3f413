package com.custommodgui.model;

import java.util.Objects;

public class Command {
    private String plugin;
    private String category;
    private String label;
    private String command;
    private boolean isCustom;

    public Command() {
        this.isCustom = false;
    }

    public Command(String plugin, String category, String label, String command) {
        this.plugin = plugin;
        this.category = category;
        this.label = label;
        this.command = command;
        this.isCustom = false;
    }

    public Command(String plugin, String category, String label, String command, boolean isCustom) {
        this.plugin = plugin;
        this.category = category;
        this.label = label;
        this.command = command;
        this.isCustom = isCustom;
    }

    // Getters
    public String getPlugin() {
        return plugin;
    }

    public String getCategory() {
        return category;
    }

    public String getLabel() {
        return label;
    }

    public String getCommand() {
        return command;
    }

    public boolean isCustom() {
        return isCustom;
    }

    // Setters
    public void setPlugin(String plugin) {
        this.plugin = plugin;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public void setCustom(boolean custom) {
        isCustom = custom;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Command command1 = (Command) o;
        return Objects.equals(plugin, command1.plugin) &&
                Objects.equals(category, command1.category) &&
                Objects.equals(label, command1.label) &&
                Objects.equals(command, command1.command);
    }

    @Override
    public int hashCode() {
        return Objects.hash(plugin, category, label, command);
    }

    @Override
    public String toString() {
        return "Command{" +
                "plugin='" + plugin + '\'' +
                ", category='" + category + '\'' +
                ", label='" + label + '\'' +
                ", command='" + command + '\'' +
                ", isCustom=" + isCustom +
                '}';
    }
}
