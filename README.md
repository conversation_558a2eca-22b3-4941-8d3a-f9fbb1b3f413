# CustomModGUI

Minecraft client-side mod pro Fabric 1.21.4, k<PERSON><PERSON> načte YAML nebo JSON soubor obsahující seznam příkazů pro různé pluginy a zobrazí je v přehledném GUI.

## 🎯 Funkce

- **Moderní GUI** s kategoriemi na levé straně a příkazovými tlačítky vpravo
- **YAML/JSON podpora** pro načítání příkazů
- **Chat konzole** v dolní části GUI pro zobrazení odeslaných příkazů
- **Vlastní příkazy** - možnost přidat vlastní příkaz přes formulář
- **Tmavý/světl<PERSON> režim** s možností přepínání
- **Animace** při přepínání kategorií a interakci s tlačítky
- **Drag & Drop** přeuspořád<PERSON><PERSON> t<PERSON> (experimentální)
- **F7 klávesa** pro otevření GUI

## 🚀 Instalace

1. <PERSON><PERSON><PERSON><PERSON><PERSON> se, že máte nainstalovaný Fabric Loader 0.16.9+ pro Minecraft 1.21.4
2. Stáhněte Fabric API 0.110.5*****.4
3. Zkompilujte mod pomocí `./gradlew build`
4. Umístěte vygenerovaný JAR soubor do složky `mods`

## 📁 Struktura konfigurace

Mod automaticky vytvoří konfigurační složku `config/custommodgui/` s následujícími soubory:

- `commands.yml` - hlavní soubor s příkazy (zkopíruje se z root adresáře)
- `custom_commands.yml` - vlastní příkazy přidané uživatelem
- `settings.yml` - nastavení modu (téma, animace, atd.)

## 📝 Formát YAML souboru

```yaml
- plugin: "CMI"
  category: "Admin"
  label: "Heal hráče"
  command: "/heal {player}"
- plugin: "CMI"
  category: "Teleport"
  label: "Teleport k hráči"
  command: "/tp {player}"
```

### Podporované parametry v příkazech:
- `{player}` - jméno hráče
- `{amount}` - množství
- `{time}` - čas
- `{reason}` - důvod
- `{item_id}` - ID předmětu
- A mnoho dalších...

## 🎮 Použití

1. Stiskněte **F7** pro otevření GUI
2. Vyberte kategorii na levé straně
3. Klikněte na příkaz pro jeho vykonání
4. Sledujte výstup v chat konzoli
5. Použijte tlačítko **"+ Přidat příkaz"** pro přidání vlastního příkazu

## ⚙️ Nastavení

- **Tmavý/světlý režim**: Tlačítko v pravém horním rohu
- **Animace**: Automaticky zapnuté, lze vypnout v `settings.yml`
- **Velikost chat historie**: Nastavitelná v `settings.yml` (výchozí: 100 zpráv)

## 🛠️ Vývoj

### Požadavky
- Java 21+
- Fabric Loader 0.16.9+
- Fabric API 0.110.5*****.4
- SnakeYAML 2.2

### Kompilace
```bash
./gradlew build
```

### Testování
```bash
./gradlew runClient
```

## 📦 Závislosti

- **Fabric Loader**: 0.16.9+
- **Fabric API**: 0.110.5*****.4
- **SnakeYAML**: 2.2 (zabaleno v modu)

## 🔧 Rozšíření

Mod je navržen pro snadné rozšíření:

- Přidání nových témat v `ThemeManager`
- Rozšíření parametrů v `CommandExecutor`
- Nové typy tlačítek v `components` balíčku
- Podpora dalších formátů souborů

## 📄 Licence

MIT License

## 🐛 Hlášení chyb

Pokud najdete chybu nebo máte návrh na vylepšení, vytvořte issue v repository.

## 🎨 Screenshoty

*TODO: Přidat screenshoty GUI v akci*

## 📚 Changelog

### v1.0.0
- Základní funkcionalita
- YAML parser
- Moderní GUI s animacemi
- Tmavý/světlý režim
- Chat konzole
- Vlastní příkazy
- F7 klávesová zkratka
