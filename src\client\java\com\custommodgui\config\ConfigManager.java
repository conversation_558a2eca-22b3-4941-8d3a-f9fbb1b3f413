package com.custommodgui.config;

import com.custommodgui.CustomModGuiClient;
import com.custommodgui.model.Command;
import net.fabricmc.loader.api.FabricLoader;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.representer.Representer;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class ConfigManager {
    private static final String CONFIG_DIR = "config/custommodgui";
    private static final String COMMANDS_FILE = "commands.yml";
    private static final String CUSTOM_COMMANDS_FILE = "custom_commands.yml";
    private static final String SETTINGS_FILE = "settings.yml";

    private static List<Command> commands = new ArrayList<>();
    private static List<Command> customCommands = new ArrayList<>();
    private static Map<String, Object> settings = new HashMap<>();

    private static Path configDir;
    private static Yaml yaml;

    public static void initialize() {
        // Vytvoření YAML instance
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);
        yaml = new Yaml(new Constructor(), new Representer(), options);

        // Vytvoření config adresáře
        configDir = Paths.get(FabricLoader.getInstance().getConfigDir().toString(), "custommodgui");
        try {
            Files.createDirectories(configDir);
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Nepodařilo se vytvořit config adresář", e);
        }

        // Načtení konfigurace
        loadCommands();
        loadCustomCommands();
        loadSettings();

        CustomModGuiClient.LOGGER.info("Načteno {} příkazů a {} vlastních příkazů", 
                commands.size(), customCommands.size());
    }

    public static void loadCommands() {
        Path commandsFile = configDir.resolve(COMMANDS_FILE);
        
        if (!Files.exists(commandsFile)) {
            createDefaultCommandsFile(commandsFile);
        }

        try (FileInputStream fis = new FileInputStream(commandsFile.toFile())) {
            List<Map<String, Object>> commandData = yaml.load(fis);
            commands.clear();
            
            if (commandData != null) {
                for (Map<String, Object> data : commandData) {
                    Command command = new Command(
                            (String) data.get("plugin"),
                            (String) data.get("category"),
                            (String) data.get("label"),
                            (String) data.get("command")
                    );
                    commands.add(command);
                }
            }
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Chyba při načítání příkazů", e);
        }
    }

    public static void loadCustomCommands() {
        Path customCommandsFile = configDir.resolve(CUSTOM_COMMANDS_FILE);
        
        if (!Files.exists(customCommandsFile)) {
            try {
                Files.createFile(customCommandsFile);
                Files.write(customCommandsFile, "[]".getBytes());
            } catch (IOException e) {
                CustomModGuiClient.LOGGER.error("Nepodařilo se vytvořit soubor vlastních příkazů", e);
            }
        }

        try (FileInputStream fis = new FileInputStream(customCommandsFile.toFile())) {
            List<Map<String, Object>> commandData = yaml.load(fis);
            customCommands.clear();
            
            if (commandData != null) {
                for (Map<String, Object> data : commandData) {
                    Command command = new Command(
                            (String) data.get("plugin"),
                            (String) data.get("category"),
                            (String) data.get("label"),
                            (String) data.get("command"),
                            true
                    );
                    customCommands.add(command);
                }
            }
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Chyba při načítání vlastních příkazů", e);
        }
    }

    public static void loadSettings() {
        Path settingsFile = configDir.resolve(SETTINGS_FILE);
        
        if (!Files.exists(settingsFile)) {
            createDefaultSettings(settingsFile);
        }

        try (FileInputStream fis = new FileInputStream(settingsFile.toFile())) {
            Map<String, Object> loadedSettings = yaml.load(fis);
            settings.clear();
            if (loadedSettings != null) {
                settings.putAll(loadedSettings);
            }
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Chyba při načítání nastavení", e);
        }
    }

    private static void createDefaultCommandsFile(Path commandsFile) {
        // Zkopírování commands.yml z workspace root do config adresáře
        Path sourceFile = Paths.get("commands.yml");
        if (Files.exists(sourceFile)) {
            try {
                Files.copy(sourceFile, commandsFile);
                CustomModGuiClient.LOGGER.info("Zkopírován commands.yml do config adresáře");
            } catch (IOException e) {
                CustomModGuiClient.LOGGER.error("Nepodařilo se zkopírovat commands.yml", e);
                createEmptyCommandsFile(commandsFile);
            }
        } else {
            createEmptyCommandsFile(commandsFile);
        }
    }

    private static void createEmptyCommandsFile(Path commandsFile) {
        try {
            Files.write(commandsFile, "[]".getBytes());
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Nepodařilo se vytvořit prázdný commands.yml", e);
        }
    }

    private static void createDefaultSettings(Path settingsFile) {
        Map<String, Object> defaultSettings = new HashMap<>();
        defaultSettings.put("darkMode", true);
        defaultSettings.put("showAnimations", true);
        defaultSettings.put("chatHistorySize", 100);
        
        try (FileWriter writer = new FileWriter(settingsFile.toFile())) {
            yaml.dump(defaultSettings, writer);
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Nepodařilo se vytvořit výchozí nastavení", e);
        }
    }

    public static void saveCustomCommands() {
        Path customCommandsFile = configDir.resolve(CUSTOM_COMMANDS_FILE);
        
        List<Map<String, Object>> commandData = new ArrayList<>();
        for (Command command : customCommands) {
            Map<String, Object> data = new HashMap<>();
            data.put("plugin", command.getPlugin());
            data.put("category", command.getCategory());
            data.put("label", command.getLabel());
            data.put("command", command.getCommand());
            commandData.add(data);
        }

        try (FileWriter writer = new FileWriter(customCommandsFile.toFile())) {
            yaml.dump(commandData, writer);
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Chyba při ukládání vlastních příkazů", e);
        }
    }

    public static void saveSettings() {
        Path settingsFile = configDir.resolve(SETTINGS_FILE);
        
        try (FileWriter writer = new FileWriter(settingsFile.toFile())) {
            yaml.dump(settings, writer);
        } catch (IOException e) {
            CustomModGuiClient.LOGGER.error("Chyba při ukládání nastavení", e);
        }
    }

    public static void addCustomCommand(Command command) {
        command.setCustom(true);
        customCommands.add(command);
        saveCustomCommands();
    }

    public static void removeCustomCommand(Command command) {
        customCommands.remove(command);
        saveCustomCommands();
    }

    // Getters
    public static List<Command> getCommands() {
        return new ArrayList<>(commands);
    }

    public static List<Command> getCustomCommands() {
        return new ArrayList<>(customCommands);
    }

    public static List<Command> getAllCommands() {
        List<Command> allCommands = new ArrayList<>(commands);
        allCommands.addAll(customCommands);
        return allCommands;
    }

    public static Map<String, Object> getSettings() {
        return new HashMap<>(settings);
    }

    public static Object getSetting(String key, Object defaultValue) {
        return settings.getOrDefault(key, defaultValue);
    }

    public static void setSetting(String key, Object value) {
        settings.put(key, value);
        saveSettings();
    }
}
